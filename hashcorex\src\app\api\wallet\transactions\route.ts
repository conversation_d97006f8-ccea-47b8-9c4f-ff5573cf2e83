import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { transactionDb, depositTransactionDb } from '@/lib/database';

// GET - Fetch user's transactions with search and filtering
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') || '';
    const status = searchParams.get('status') || '';
    const dateFrom = searchParams.get('dateFrom') || '';
    const dateTo = searchParams.get('dateTo') || '';

    // Build where clause for transactions
    const where: any = { userId: user.id };
    
    if (type && type !== 'ALL') {
      where.type = type;
    }
    
    if (status && status !== 'ALL') {
      where.status = status;
    }

    // Date filtering
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo + 'T23:59:59.999Z');
      }
    }

    // Get transactions
    let transactions = await transactionDb.findByUserId(user.id, {
      limit: Math.min(limit, 100),
      offset,
    });

    // Apply search filter if provided
    if (search) {
      const searchLower = search.toLowerCase();
      transactions = transactions.filter(tx => 
        tx.description.toLowerCase().includes(searchLower) ||
        tx.type.toLowerCase().includes(searchLower) ||
        tx.status.toLowerCase().includes(searchLower) ||
        (tx.reference && tx.reference.toLowerCase().includes(searchLower))
      );
    }

    // Apply type filter
    if (type && type !== 'ALL') {
      transactions = transactions.filter(tx => tx.type === type);
    }

    // Apply status filter
    if (status && status !== 'ALL') {
      transactions = transactions.filter(tx => tx.status === status);
    }

    // Apply date filters
    if (dateFrom || dateTo) {
      transactions = transactions.filter(tx => {
        const txDate = new Date(tx.createdAt);
        if (dateFrom && txDate < new Date(dateFrom)) return false;
        if (dateTo && txDate > new Date(dateTo + 'T23:59:59.999Z')) return false;
        return true;
      });
    }

    // Get recent deposits for deposit transactions
    const recentDeposits = await depositTransactionDb.findByUserId(user.id, { limit: 50 });

    // Combine and format all transactions
    const allTransactions = [
      ...transactions.map(tx => ({
        id: tx.id,
        type: tx.type,
        category: 'TRANSACTION',
        amount: tx.amount,
        description: tx.description,
        status: tx.status,
        reference: tx.reference,
        createdAt: tx.createdAt,
      })),
      ...recentDeposits.map(deposit => ({
        id: deposit.id,
        type: 'DEPOSIT',
        category: 'DEPOSIT',
        amount: deposit.usdtAmount,
        description: `USDT TRC20 Deposit - TX: ${deposit.transactionId.substring(0, 20)}...`,
        status: deposit.status,
        reference: deposit.transactionId,
        createdAt: deposit.createdAt,
      }))
    ];

    // Sort by creation date (newest first)
    allTransactions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Apply pagination to combined results
    const paginatedTransactions = allTransactions.slice(offset, offset + limit);

    // Get transaction type options for filtering
    const transactionTypes = [
      'ALL',
      'MINING_EARNINGS',
      'DIRECT_REFERRAL', 
      'BINARY_BONUS',
      'DEPOSIT',
      'WITHDRAWAL',
      'PURCHASE',
      'ADMIN_CREDIT',
      'ADMIN_DEBIT'
    ];

    const statusOptions = [
      'ALL',
      'PENDING',
      'COMPLETED',
      'FAILED',
      'CANCELLED',
      'CONFIRMED',
      'PENDING_VERIFICATION',
      'WAITING_FOR_CONFIRMATIONS'
    ];

    return NextResponse.json({
      success: true,
      data: {
        transactions: paginatedTransactions,
        pagination: {
          limit,
          offset,
          total: allTransactions.length,
          hasMore: offset + limit < allTransactions.length,
        },
        filters: {
          transactionTypes,
          statusOptions,
        },
      },
    });

  } catch (error: any) {
    console.error('Wallet transactions fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wallet transactions' },
      { status: 500 }
    );
  }
}
